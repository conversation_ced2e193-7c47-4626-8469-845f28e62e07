import type { InputSchema } from "../client/yt-dlp";
import type { YtDlpInfoJSONSchema } from "../client/yt-dlp/info";
import {
  actRunsPost,
  datasetItemsGet,
  datasetDelete,
  type ErrorResponse,
} from "../client/apify";
import djb2a from "@mx/shared/utils/djb2a";
import { reduceAspectRatio } from "../lib/math.js";
import { getLogger } from "@logtape/logtape";
import { redactSensitiveData } from "../lib/logging";

const logger = getLogger(["mx-api", "apify"]);

export interface ApifyConfig {
  actorId: string;
  token: string;
  apiHost: string;
}

export interface YtDlpYoutubeSubtitle {
  id: string;
  lang: string;
  ast: boolean;
  name: string | undefined;
  url: string;
}

export type YtDlpYouTubeMetadata = {
  version: "yt_dlp_v1";
  language: string;
  aspect_ratio: string;
  /**
   * in seconds
   */
  duration: number;
  title: string;
  description: string;
  tags: string[];
  /**
   * YYYYMMDD format
   */
  upload_date: string;
  uploader_name: string;
  /**
   * public uid of the uploader
   * @example "@stanford"
   */
  uploader_id: string;
  view_count: number | null;
  like_count: number | null;
  comment_count: number | null;
  chapters: {
    title: string;
    /** in seconds */
    end_time: number;
    /** in seconds */
    start_time: number;
  }[];
};

export type ProcessedVideoData = {
  metadata: YtDlpYouTubeMetadata;
  subtitles: YtDlpYoutubeSubtitle[];
};

export type ActionRunResult =
  | {
      video_id: string;
      video_url: string;
      attempts_made: number;
      extraction_success: false;
      error: string;
    }
  | {
      video_id: string;
      video_url: string;
      attempts_made: number;
      extraction_success: true;
      info: YtDlpInfoJSONSchema;
      proxy_used: boolean;
    };

import * as v from "valibot";
import { pick } from "@std/collections";

export const webhookResponseSchema = {
  success: v.object({
    lockId: v.string(),
    videoUrl: v.pipe(v.string(), v.url()),
    runId: v.string(),
    status: v.string(),
    datasetId: v.string(),
  }),
  failed: v.object({
    lockId: v.string(),
    videoUrl: v.pipe(v.string(), v.url()),
    runId: v.string(),
    status: v.string(),
    error: v.string(),
  }),
};

export type WebhookResponseSuccess = v.InferOutput<
  typeof webhookResponseSchema.success
>;

export type WebhookResponseFailed = v.InferOutput<
  typeof webhookResponseSchema.failed
>;

export class ApifyService {
  #config: ApifyConfig;

  constructor(env: Cloudflare.Env) {
    this.#config = {
      actorId: env.YTDLP_ACTOR_ID,
      token: env.APIFY_TOKEN,
      apiHost: env.API_HOST,
    };
  }

  /**
   * Start Apify actor run with webhook integration
   */
  async startActorRun({
    lockId,
    videoUrl,
  }: { lockId: string; videoUrl: string }): Promise<void> {
    const startTime = performance.now();
    logger.info("Starting Apify actor run", {
      lockId,
      videoUrl,
      actorId: this.#config.actorId,
    });

    const webhooks = generateWebhooks(videoUrl, {
      lockId,
      successUrl: `${this.#config.apiHost}/youtube/webhook/success`,
      failedUrl: `${this.#config.apiHost}/youtube/webhook/failed`,
    });

    logger.debug("Generated webhooks for actor run", {
      lockId,
      successUrl: `${this.#config.apiHost}/youtube/webhook/success`,
      failedUrl: `${this.#config.apiHost}/youtube/webhook/failed`,
    });

    const runResp = await actRunsPost({
      auth: this.#config.token,
      path: { actorId: this.#config.actorId },
      body: {
        youtube_url: videoUrl,
        use_apify_proxy: true,
      } satisfies InputSchema,
      query: {
        webhooks: webhooks,
      },
    }).then(handleApifyResp);

    const run = runResp.data;
    const duration = performance.now() - startTime;

    logger.info("Apify actor run started successfully", {
      lockId,
      videoUrl,
      runId: run.id,
      duration: Math.round(duration),
    });
  }

  /**
   * Process dataset data after successful actor run
   */
  async getRunResult(datasetId: string): Promise<ProcessedVideoData> {
    const startTime = performance.now();
    logger.info("Processing Apify run result", { datasetId });

    // Fetch dataset items
    logger.debug("Fetching dataset items", { datasetId });
    const dataset = await datasetItemsGet({
      auth: this.#config.token,
      path: { datasetId },
    }).then(handleApifyResp);

    // Process the extraction data
    const [result] = dataset as ActionRunResult[];

    if (!result) {
      logger.error("No valid extraction data found", { datasetId });
      throw new Error("Failed to extract valid data");
    }
    if (result.extraction_success === false) {
      logger.error("Extraction failed", {
        datasetId,
        error: result.error,
        attempts: result.attempts_made,
      });
      throw new Error(result.error);
    }

    logger.debug("Extraction successful", {
      datasetId,
      videoId: result.video_id,
      attempts: result.attempts_made,
      proxyUsed: result.proxy_used,
    });

    const infoJson = result.info;
    if (!infoJson) {
      logger.error("No valid info JSON found", { datasetId });
      throw new Error("No valid info JSON found");
    }

    logger.debug("Processing yt-dlp data", {
      datasetId,
      videoId: result.video_id,
      title: infoJson.title,
    });

    const processedData = handleYtDlpData(infoJson);

    const duration = performance.now() - startTime;
    logger.info("Run result processing completed", {
      datasetId,
      videoId: result.video_id,
      subtitleCount: processedData.subtitles.length,
      duration: Math.round(duration),
    });

    return processedData;
  }

  /**
   * Clean up dataset after processing
   */
  async cleanupDataset(datasetId: string): Promise<void> {
    const startTime = performance.now();
    logger.debug("Cleaning up dataset", { datasetId });

    await datasetDelete({
      auth: this.#config.token,
      path: { datasetId },
    }).then(handleApifyResp);

    const duration = performance.now() - startTime;
    logger.debug("Dataset cleanup completed", {
      datasetId,
      duration: Math.round(duration),
    });
  }
}

function handleYtDlpData(infoJson: YtDlpInfoJSONSchema): ProcessedVideoData {
  logger.debug("Processing yt-dlp info JSON", {
    videoId: infoJson.id,
    title: infoJson.title,
    subtitleLanguages: Object.keys(infoJson.subtitles ?? {}),
    autoSubtitleLanguages: Object.keys(infoJson.automatic_captions ?? {}),
  });

  const subtitles = Object.entries(infoJson.subtitles ?? {}).map(
    ([lang, formats]) => {
      const subtitle = formats.find((v) => v.ext === "json3");
      if (!subtitle) return null;
      return {
        lang,
        ast: false,
        name: subtitle.name,
        url: subtitle.url,
      };
    },
  );

  const auto_generated_subtitles = Object.entries(
    infoJson.automatic_captions ?? {},
  ).map(([lang, formats]) => {
    const subtitle = formats.find((v) => v.ext === "json3");
    if (!subtitle) return null;
    return {
      lang,
      ast: true,
      name: subtitle.name,
      url: subtitle.url,
    };
  });

  const processedSubtitles = [
    ...subtitles.filter((v) => !!v),
    ...auto_generated_subtitles.filter((v) => !!v),
  ].map(
    (v): YtDlpYoutubeSubtitle => ({
      ...v,
      id: djb2a(`${v.lang}:${v.ast}`).toString(36),
    }),
  );

  logger.debug("yt-dlp data processing completed", {
    videoId: infoJson.id,
    subtitleCount: processedSubtitles.length,
    manualSubtitles: subtitles.filter((v) => !!v).length,
    autoSubtitles: auto_generated_subtitles.filter((v) => !!v).length,
    chapterCount: infoJson.chapters?.length ?? 0,
  });

  return {
    metadata: {
      version: "yt_dlp_v1",
      language: infoJson.language ?? "",
      aspect_ratio: reduceAspectRatio(
        infoJson.width ?? 0,
        infoJson.height ?? 0,
      ),
      duration: infoJson.duration ?? 0,
      title: infoJson.title ?? "",
      description: infoJson.description ?? "",
      tags: infoJson.tags || [],
      upload_date: infoJson.upload_date ?? "",
      uploader_name: infoJson.uploader ?? "",
      uploader_id: infoJson.uploader_id ?? "",
      chapters:
        infoJson.chapters?.map((v) =>
          pick(v, ["title", "end_time", "start_time"]),
        ) ?? [],
      view_count: infoJson.view_count || null,
      like_count: infoJson.like_count || null,
      comment_count: infoJson.comment_count || null,
    },
    subtitles: processedSubtitles,
  };
}

function generateWebhooks(
  videoUrl: string,
  {
    lockId,
    successUrl,
    failedUrl,
  }: { lockId: string; successUrl: string; failedUrl: string },
): string {
  const webhooks = [
    {
      eventTypes: ["ACTOR.RUN.SUCCEEDED"],
      requestUrl: successUrl,
      payloadTemplate: JSON.stringify({
        lockId,
        videoUrl,
        runId: "{{resource.id}}",
        status: "{{resource.status}}",
        datasetId: "{{resource.defaultDatasetId}}",
      } satisfies WebhookResponseSuccess),
      headersTemplate: JSON.stringify({
        "Content-Type": "application/json",
      }),
    },
    {
      eventTypes: [
        "ACTOR.RUN.FAILED",
        "ACTOR.RUN.ABORTED",
        "ACTOR.RUN.TIMED_OUT",
      ],
      requestUrl: failedUrl,
      payloadTemplate: JSON.stringify({
        lockId,
        videoUrl,
        runId: "{{resource.id}}",
        status: "{{resource.status}}",
        error: "{{resource.statusMessage}}",
      } satisfies WebhookResponseFailed),
      headersTemplate: JSON.stringify({
        "Content-Type": "application/json",
      }),
    },
  ];

  return btoa(
    JSON.stringify(webhooks).replaceAll(/\\"(\{\{[\w.]+\}\})\\"/g, "$1"),
  );
}

function handleApifyResp<T>(
  resp: (
    | {
        data: T;
        error: undefined;
      }
    | {
        data: undefined;
        error: unknown;
      }
  ) & { response: Response },
): T {
  if (!resp.error) {
    logger.trace("Apify API response success", {
      status: resp.response.status,
      url: resp.response.url,
    });
    return resp.data!;
  }

  logger.error("Apify API response error", {
    status: resp.response.status,
    statusText: resp.response.statusText,
    error: resp.error,
    url: resp.response.url,
  });
  throw new ApifyError(resp);
}

export class ApifyError extends Error {
  type;
  constructor(resp: {
    error: unknown;
    response: Response;
  }) {
    const error = resp.error as ErrorResponse;
    if ("error" in error) {
      super(`Apify API error (${error.error.type}): ${error.error.message}`);
      this.type = error.error.type;
    } else {
      super(
        `Apify API error: ${resp.response.status} ${resp.response.statusText}`,
      );
      this.type = "unknown";
    }
  }
}

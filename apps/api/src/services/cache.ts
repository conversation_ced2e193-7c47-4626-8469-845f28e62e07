import { nanoid } from "nanoid";
import type { YtDlpYouTubeMetadata, YtDlpYoutubeSubtitle } from "./apify";
import type { YouTubeApiMetadata, YouTubeApiSubtitle } from "./youtube-api";
import type { Pipeline, Redis } from "@upstash/redis/cloudflare";
import { pick } from "@std/collections";
import { getLogger } from "@logtape/logtape";
import { redactSensitiveData } from "../lib/logging";

const logger = getLogger(["mx-api", "cache"]);

// Simplified cache types without expiration tracking - Redis handles expiration

// Subtitle list cache without expiration tracking
export type YtdlpVideoSubtitleListCache = ({
  id: string;
} & YtdlpVideoSubtitleListCacheItem)[];

interface YtdlpVideoSubtitleListCacheItem {
  ast: boolean;
  name: string | undefined;
}

export const HASH_CACHEAT_KEY = "__CACHED_AT__" as const;

// Cache result types for stale-while-revalidate logic
export type CacheResult<T> = {
  data: T;
  isStale: boolean;
} | null;

// Lock status for distributed locking - Redis handles expiration
export type VideoLockStatus = {
  lock_id: string;
  video_id: string;
  status: "processing" | "completed" | "failed";
  run_id?: string;
  error?: string;
};

// Cache key patterns
export const CACHE_KEYS = {
  metadata: (videoId: string) => `yt-dlp:metadata:${videoId}`,
  subtitleUrlHash: (videoId: string) => `yt-dlp:subtitles-urls:${videoId}`,
  subtitleListHash: (videoId: string) => `yt-dlp:subtitles-list:${videoId}`,
  lock: (videoId: string) => `yt-dlp:lock:${videoId}`,
  // YouTube API cache keys
  youtubeApiMetadata: (videoId: string) => `yt-api:metadata:${videoId}`,
  youtubeApiSubtitles: (videoId: string) => `yt-api:subtitles:${videoId}`,
} as const;

// Default expiration times (in seconds)
export const CACHE_EXPIRATION = {
  metadata: 7 * 24 * 60 * 60, // 7 days
  subtitleList: 24 * 60 * 60, // 1 day
  subtitle: 4 * 60 * 60, // 4 hours (fallback, prefer URL expiration, 7 hours in general)
  lock: 3 * 60, // 3 minutes (max processing time)
  // YouTube API cache expiration
  youtubeApi: 6 * 60 * 60, // 6 hours
  youtubeApiSubtitles: 7 * 24 * 60 * 60, // 7 days, it take 50 quota per call...
} as const;

/**
 * Comprehensive cache service with distributed locking for Apify action management
 * Uses Redis TTL for automatic cache invalidation
 * Implements stale-while-revalidate logic for optimal performance
 */
export class VideoCacheService {
  #redis: Redis;
  pipeline() {
    return this.#redis.pipeline();
  }

  constructor(redis: Redis) {
    this.#redis = redis;
  }

  /**
   * Generate a unique lock ID for a video processing request
   */
  generateLockId(videoId: string): string {
    return `${videoId}-${nanoid(10)}`;
  }

  /**
   * Check if cache is stale based on TTL (< 25% of original expiration time)
   * @param ttl - TTL in seconds
   * @param originalExpiration - Original expiration time in seconds
   * @returns true if cache is stale, false otherwise
   */
  #isStale(
    ttl: number,
    expiry: { exat: number; cached_at: number } | { ex: number },
  ): boolean {
    if (ttl <= 0) return true; // Expired or doesn't exist
    const expiryDuration =
      "exat" in expiry ? expiry.exat - expiry.cached_at : expiry.ex;
    return ttl < expiryDuration * 0.25;
  }

  /**
   * Acquire distributed lock for video processing
   * Returns lock info if successful, null if already locked
   */
  async acquireLock(videoId: string): Promise<VideoLockStatus | null> {
    const startTime = performance.now();
    logger.info("Attempting to acquire video processing lock", { videoId });

    const lockKey = CACHE_KEYS.lock(videoId);
    const lockId = this.generateLockId(videoId);

    const lockStatus: VideoLockStatus = {
      lock_id: lockId,
      video_id: videoId,
      status: "processing",
    };

    logger.debug("Attempting Redis lock acquisition", {
      videoId,
      lockId,
      lockKey,
      expiration: CACHE_EXPIRATION.lock,
    });

    // Use Redis SET with NX (only if not exists) and EX (expiration)
    const result = await this.#redis.set(lockKey, lockStatus, {
      nx: true, // Only set if key doesn't exist
      ex: CACHE_EXPIRATION.lock, // Expire after lock timeout
    });

    const duration = performance.now() - startTime;
    const success = result === "OK";

    if (success) {
      logger.info("Video processing lock acquired successfully", {
        videoId,
        lockId,
        status: lockStatus.status,
        duration: Math.round(duration),
      });
      return lockStatus;
    }

    logger.warn("Failed to acquire video processing lock - already exists", {
      videoId,
      lockId,
      duration: Math.round(duration),
    });
    return null;
  }

  /**
   * Get current lock status for a video
   */
  async getLockStatus(videoId: string): Promise<VideoLockStatus | null> {
    const startTime = performance.now();
    logger.debug("Checking video lock status", { videoId });

    const lockKey = CACHE_KEYS.lock(videoId);
    const lockData = await this.#redis.get<VideoLockStatus>(lockKey);

    const duration = performance.now() - startTime;

    if (lockData) {
      logger.debug("Video lock found", {
        videoId,
        lockId: lockData.lock_id,
        status: lockData.status,
        runId: lockData.run_id,
        hasError: !!lockData.error,
        duration: Math.round(duration),
      });
    } else {
      logger.debug("No video lock found", {
        videoId,
        duration: Math.round(duration),
      });
    }

    return lockData;
  }

  /**
   * Release lock (called by webhooks on success/failure)
   */
  async releaseLock(
    lockId: string,
    videoId: string,
    status: "completed" | "failed",
    error?: string,
  ): Promise<boolean> {
    const startTime = performance.now();
    logger.info("Attempting to release video processing lock", {
      videoId,
      lockId,
      status,
      hasError: !!error,
    });

    const lockKey = CACHE_KEYS.lock(videoId);
    const currentLock = await this.getLockStatus(videoId);

    if (!currentLock || currentLock.lock_id !== lockId) {
      const duration = performance.now() - startTime;
      logger.warn("Lock release failed - mismatch or not found", {
        lockId,
        videoId,
        currentLockId: currentLock?.lock_id,
        currentStatus: currentLock?.status,
        duration: Math.round(duration),
      });
      return false;
    }

    // Update lock status before deletion
    const updatedLock: VideoLockStatus = {
      ...currentLock,
      status,
      error,
    };

    logger.debug("Updating lock status before expiration", {
      videoId,
      lockId,
      oldStatus: currentLock.status,
      newStatus: status,
      retentionTime: 60,
    });

    // Keep for 1 minute for debugging, Redis will auto-expire
    await this.#redis.set(lockKey, updatedLock, { ex: 60 });

    const duration = performance.now() - startTime;
    logger.info("Video processing lock released successfully", {
      videoId,
      lockId,
      status,
      duration: Math.round(duration),
    });

    return true;
  }

  /**
   * Get cached metadata with stale check using Redis pipeline
   */
  async getMetadata(
    videoId: string,
  ): Promise<CacheResult<YtDlpYouTubeMetadata>> {
    const startTime = performance.now();
    logger.debug("Fetching cached yt-dlp metadata", { videoId });

    const cacheKey = CACHE_KEYS.metadata(videoId);

    // Use Redis pipeline to get both data and TTL in single call
    const [data, ttl] = await this.#redis
      .pipeline()
      .get<YtDlpYouTubeMetadata>(cacheKey)
      .ttl(cacheKey)
      .exec();

    const duration = performance.now() - startTime;

    if (!data) {
      logger.debug("yt-dlp metadata cache miss", {
        videoId,
        duration: Math.round(duration),
      });
      return null;
    }

    const isStale = this.#isStale(ttl, { ex: CACHE_EXPIRATION.metadata });

    logger.info("yt-dlp metadata cache hit", {
      videoId,
      title: data.title,
      isStale,
      ttl,
      duration: Math.round(duration),
    });

    return {
      data,
      isStale,
    };
  }

  /**
   * Cache metadata with Redis TTL
   */
  async setMetadata(
    videoId: string,
    metadata: YtDlpYouTubeMetadata,
    { redis = this.#redis }: { redis?: Redis | Pipeline } = {},
  ): Promise<void> {
    const startTime = performance.now();
    logger.info("Caching yt-dlp metadata", {
      videoId,
      title: metadata.title,
      duration: metadata.duration,
      expiration: CACHE_EXPIRATION.metadata,
    });

    const cacheKey = CACHE_KEYS.metadata(videoId);
    await redis.set<YtDlpYouTubeMetadata>(cacheKey, metadata, {
      ex: CACHE_EXPIRATION.metadata,
    });

    const duration = performance.now() - startTime;
    logger.debug("yt-dlp metadata cached successfully", {
      videoId,
      title: metadata.title,
      duration: Math.round(duration),
    });
  }

  /**
   * Get cached subtitle list with stale check using Redis pipeline
   */
  async getSubtitleList(
    videoId: string,
  ): Promise<CacheResult<YtdlpVideoSubtitleListCache>> {
    if (videoId === HASH_CACHEAT_KEY) return null;

    const startTime = performance.now();
    logger.debug("Fetching cached subtitle list", { videoId });

    const hashKey = CACHE_KEYS.subtitleListHash(videoId);

    // Get all hash fields and TTL in a single pipeline
    const [hashData, hashTtl] = await this.#redis
      .pipeline()
      .hgetall(hashKey)
      .ttl(hashKey)
      .exec();

    const duration = performance.now() - startTime;

    if (!hashData || Object.keys(hashData).length === 1) {
      logger.debug("Subtitle list cache miss", {
        videoId,
        hasData: !!hashData,
        keyCount: hashData ? Object.keys(hashData).length : 0,
        duration: Math.round(duration),
      });
      return null;
    }

    // Filter out the cache metadata key and convert to subtitle list format
    const subtitleEntries = Object.entries(hashData).filter(
      (entries): entries is [string, YtdlpVideoSubtitleListCacheItem] =>
        entries[0] !== HASH_CACHEAT_KEY,
    );

    if (subtitleEntries.length === 0) {
      logger.debug("Subtitle list cache empty after filtering", {
        videoId,
        totalKeys: Object.keys(hashData).length,
        duration: Math.round(duration),
      });
      return null;
    }

    const data: YtdlpVideoSubtitleListCache = subtitleEntries.map(
      ([id, value]) => ({ ...value, id }),
    );

    const isStale = this.#isStale(hashTtl, {
      ex: CACHE_EXPIRATION.subtitleList,
    });

    logger.info("Subtitle list cache hit", {
      videoId,
      subtitleCount: data.length,
      astCount: data.filter((s) => s.ast).length,
      isStale,
      ttl: hashTtl,
      duration: Math.round(duration),
    });

    return {
      data,
      isStale,
    };
  }

  /**
   * Cache subtitle list with Redis TTL using hash storage
   */
  async setSubtitleList(
    videoId: string,
    subtitles: YtDlpYoutubeSubtitle[],
    { redis = this.#redis }: { redis?: Redis | Pipeline } = {},
  ): Promise<void> {
    if (subtitles.length === 0) return;

    const hashKey = CACHE_KEYS.subtitleListHash(videoId);
    const records = {
      ...Object.fromEntries(
        subtitles
          .filter((v) => v.id !== HASH_CACHEAT_KEY)
          .map((s): [string, YtdlpVideoSubtitleListCacheItem] => [
            s.id,
            pick(s, ["ast", "name"]),
          ]),
      ),
      [HASH_CACHEAT_KEY]: Math.floor(Date.now() / 1000),
    };

    redis.hset<number | string>(hashKey, records);
    redis.expire(hashKey, CACHE_EXPIRATION.subtitleList);
  }

  /**
   * Check if a specific subtitle exists in the cached subtitle list
   */
  async hasSubtitle(
    videoId: string,
    subtitleId: string,
  ): Promise<boolean | null> {
    if (subtitleId === HASH_CACHEAT_KEY) return false;

    const hashKey = CACHE_KEYS.subtitleListHash(videoId);
    const [cacheExists, hashExists] = await this.#redis
      .pipeline()
      .exists(hashKey)
      .hexists(hashKey, subtitleId)
      .exec();

    if (cacheExists === 0) return null;
    return cacheExists === 1 && hashExists === 1;
  }

  /**
   * Get cached subtitle URL (checks hash-based storage first, then individual cache)
   */
  async getSubtitleUrl(
    videoId: string,
    subtitleId: string,
  ): Promise<{ url: string; isStale: boolean } | null> {
    if (subtitleId === HASH_CACHEAT_KEY) return null;

    const startTime = performance.now();
    logger.debug("Fetching cached subtitle URL", { videoId, subtitleId });

    // First check hash-based storage
    const hashKey = CACHE_KEYS.subtitleUrlHash(videoId);
    const [url, cachedAt, hashTtl, [keyTtl]] = await this.#redis
      .pipeline()
      .hget<string>(hashKey, subtitleId)
      .hget<number>(hashKey, HASH_CACHEAT_KEY)
      .ttl(hashKey)
      .httl(hashKey, subtitleId)
      .exec();

    const duration = performance.now() - startTime;

    if (!url) {
      logger.debug("Subtitle URL cache miss", {
        videoId,
        subtitleId,
        duration: Math.round(duration),
      });
      return null;
    }

    const expiry = parseExpirationFromSubtitleUrl(url);

    if (expiry.exat === null) {
      const isStale = this.#isStale(keyTtl ?? hashTtl ?? 0, {
        ex: CACHE_EXPIRATION.subtitle,
      });

      logger.info("Subtitle URL cache hit (no URL expiration)", {
        videoId,
        subtitleId,
        isStale,
        ttl: keyTtl ?? hashTtl,
        duration: Math.round(duration),
      });

      return { url, isStale };
    }

    if (cachedAt === null) {
      logger.warn("No cached_at found for subtitle hash", {
        videoId,
        subtitleId,
        hashKey,
        duration: Math.round(duration),
      });
      return { url, isStale: false };
    }

    const isStale = this.#isStale(keyTtl ?? hashTtl ?? 0, {
      exat: expiry.exat,
      cached_at: cachedAt,
    });

    logger.info("Subtitle URL cache hit (with URL expiration)", {
      videoId,
      subtitleId,
      isStale,
      urlExpiration: expiry.exat,
      cachedAt,
      ttl: keyTtl ?? hashTtl,
      duration: Math.round(duration),
    });

    return { url, isStale };
  }

  /**
   * Cache multiple subtitle URLs in a hash with shared minimum expiration
   * Logs min/max/avg expiration statistics
   */
  async setSubtitleUrlsHash(
    videoId: string,
    subtitles: { id: string; url: string }[],
    { redis = this.#redis }: { redis?: Redis | Pipeline } = {},
  ): Promise<void> {
    if (subtitles.length === 0) return;
    const hashKey = CACHE_KEYS.subtitleUrlHash(videoId);
    const records = {
      ...Object.fromEntries(
        subtitles
          .filter((v) => v.id !== HASH_CACHEAT_KEY)
          .map((s) => [s.id, s.url]),
      ),
      // use unix timestamp in seconds
      [HASH_CACHEAT_KEY]: Math.floor(Date.now() / 1000),
    };
    redis.hset<number | string>(hashKey, records);
    const expirations = Map.groupBy(
      subtitles.map((s) => ({
        ...parseExpirationFromSubtitleUrl(s.url),
        ...s,
      })),
      (e) => e.exat,
    );
    // if the whole set of url shared the same expiration, set the expiration at hash key
    if (expirations.size === 1) {
      if (expirations.get(null)) {
        redis.expire(hashKey, CACHE_EXPIRATION.subtitle);
      } else {
        const [grouped] = expirations.values();
        const exat = grouped![0]!.exat!;
        redis.expireat(hashKey, exat);
      }
    } else {
      for (const [exat, grouped] of expirations) {
        if (exat === null) {
          redis.hexpire(
            hashKey,
            grouped.map((e) => e.id),
            CACHE_EXPIRATION.subtitle,
          );
        } else {
          redis.hexpireat(
            hashKey,
            grouped.map((e) => e.id),
            exat,
          );
        }
      }
    }
  }

  /**
   * Get cached YouTube API metadata with stale check
   */
  async getYouTubeApiMetadata(
    videoId: string,
  ): Promise<CacheResult<YouTubeApiMetadata>> {
    const startTime = performance.now();
    logger.debug("Fetching cached YouTube API metadata", { videoId });

    const cacheKey = CACHE_KEYS.youtubeApiMetadata(videoId);

    const [data, ttl] = await this.#redis
      .pipeline()
      .get<YouTubeApiMetadata>(cacheKey)
      .ttl(cacheKey)
      .exec();

    const duration = performance.now() - startTime;

    if (!data) {
      logger.debug("YouTube API metadata cache miss", {
        videoId,
        duration: Math.round(duration),
      });
      return null;
    }

    const isStale = this.#isStale(ttl, { ex: CACHE_EXPIRATION.youtubeApi });

    logger.info("YouTube API metadata cache hit", {
      videoId,
      title: data.title,
      isStale,
      ttl,
      duration: Math.round(duration),
    });

    return {
      data,
      isStale,
    };
  }

  /**
   * Cache YouTube API metadata with Redis TTL
   */
  async setYouTubeApiMetadata(
    videoId: string,
    metadata: YouTubeApiMetadata,
    { redis = this.#redis }: { redis?: Redis | Pipeline } = {},
  ): Promise<void> {
    const cacheKey = CACHE_KEYS.youtubeApiMetadata(videoId);
    await redis.set(cacheKey, metadata, {
      ex: CACHE_EXPIRATION.youtubeApi,
    });
  }

  /**
   * Get cached YouTube API subtitle list with stale check
   */
  async getYouTubeApiSubtitleList(
    videoId: string,
  ): Promise<CacheResult<YouTubeApiSubtitle[]>> {
    const cacheKey = CACHE_KEYS.youtubeApiSubtitles(videoId);

    // Use Redis pipeline to get both data and TTL in single call
    const [data, ttl] = await this.#redis
      .pipeline()
      .get<YouTubeApiSubtitle[]>(cacheKey)
      .ttl(cacheKey)
      .exec();

    if (!data) return null;

    const isStale = this.#isStale(ttl, { ex: CACHE_EXPIRATION.youtubeApi });

    return {
      data,
      isStale,
    };
  }

  /**
   * Cache YouTube API subtitle list with Redis TTL
   */
  async setYouTubeApiSubtitleList(
    videoId: string,
    subtitles: YouTubeApiSubtitle[],
    { redis = this.#redis }: { redis?: Redis | Pipeline } = {},
  ): Promise<void> {
    const cacheKey = CACHE_KEYS.youtubeApiSubtitles(videoId);
    await redis.set(cacheKey, subtitles, {
      ex: CACHE_EXPIRATION.youtubeApiSubtitles,
    });
  }
}

/**
 * Parse expiration seconds from YouTube URL or use fallback
 * @returns expiration unix timestamp in seconds
 */
function parseExpirationFromSubtitleUrl(url: string): { exat: number | null } {
  try {
    const urlObj = new URL(url);
    const expires = urlObj.searchParams.get("expire");
    if (!expires) throw new Error("No expiration found");

    const expirationTimestamp = Number.parseInt(expires, 10);
    if (Number.isNaN(expirationTimestamp))
      throw new Error("Invalid expiration timestamp");

    return { exat: expirationTimestamp };
  } catch (error) {
    logger.warn("Failed to parse YouTube URL expiration", {
      url: redactSensitiveData({ url }).url,
      error: error instanceof Error ? error.message : String(error),
    });
    return { exat: null };
  }
}

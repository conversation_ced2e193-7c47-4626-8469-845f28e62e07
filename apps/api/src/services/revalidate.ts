import type { ExecutionContext } from "hono";
import { retry } from "@std/async";
import type { ApifyService } from "./apify";
import type { VideoCacheService } from "./cache";
import type { YouTubeDataAPIService } from "./youtube-api";
import { getLogger } from "@logtape/logtape";

const logger = getLogger(["mx-api", "revalidate"]);

// Generic task function type for better type safety
export type TaskFn<T = void> = (context: TaskContext) => Promise<T>;

// Context passed to all tasks
export interface TaskContext {
  videoId: string;
  services: {
    youtubeApi: YouTubeDataAPIService;
    cache: VideoCacheService;
    apify: ApifyService;
  };
}

// Configuration for task execution
export interface TaskExecutionOptions {
  maxAttempts?: number;
  minTimeout?: number;
  multiplier?: number;
}

// Default execution options
const DEFAULT_TASK_OPTIONS: Required<TaskExecutionOptions> = {
  maxAttempts: 3,
  minTimeout: 1000,
  multiplier: 2,
};

/**
 * Generic task executor that handles retry logic and background execution
 */
export class TaskExecutor {
  #executionCtx: ExecutionContext;
  #options: Required<TaskExecutionOptions>;

  constructor(
    executionCtx: ExecutionContext,
    options: TaskExecutionOptions = {},
  ) {
    this.#executionCtx = executionCtx;
    this.#options = { ...DEFAULT_TASK_OPTIONS, ...options };
  }

  /**
   * Execute a task in the background with retry logic
   */
  enqueue<T>(task: TaskFn<T>, context: TaskContext): void {
    const strategy = () => task(context);

    logger.debug("Enqueuing task for execution", {
      videoId: context.videoId,
      taskName: task.name || "anonymous",
      maxAttempts: this.#options.maxAttempts,
    });

    this.#executionCtx.waitUntil(
      retry(strategy, {
        maxAttempts: this.#options.maxAttempts,
        minTimeout: this.#options.minTimeout,
        multiplier: this.#options.multiplier,
      }).catch((error) => {
        logger.error("Task execution failed after retries", {
          videoId: context.videoId,
          taskName: task.name || "anonymous",
          error: error instanceof Error ? error.message : String(error),
          maxAttempts: this.#options.maxAttempts,
        });
        throw error;
      }),
    );
  }

  /**
   * Execute multiple tasks in parallel
   */
  enqueueAll<T>(tasks: TaskFn<T>[], context: TaskContext): void {
    const parallelStrategy = () =>
      Promise.all(tasks.map((task) => task(context)));

    const taskNames = tasks.map((task) => task.name || "anonymous");
    logger.debug("Enqueuing multiple tasks for parallel execution", {
      videoId: context.videoId,
      taskCount: tasks.length,
      taskNames,
      maxAttempts: this.#options.maxAttempts,
    });

    this.#executionCtx.waitUntil(
      retry(parallelStrategy, {
        maxAttempts: this.#options.maxAttempts,
        minTimeout: this.#options.minTimeout,
        multiplier: this.#options.multiplier,
      }).catch((error) => {
        logger.error("Parallel task execution failed after retries", {
          videoId: context.videoId,
          taskCount: tasks.length,
          taskNames,
          error: error instanceof Error ? error.message : String(error),
          maxAttempts: this.#options.maxAttempts,
        });
        throw error;
      }),
    );
  }
}

/**
 * Predefined tasks for YouTube video processing
 */
export const YouTubeTasks = {
  /**
   * Fetch and cache YouTube API subtitle list (blocking)
   */
  fetchApiSubtitles: async ({
    videoId,
    services,
  }: TaskContext): Promise<void> => {
    const startTime = performance.now();
    logger.debug("Starting API subtitles fetch", { videoId });

    const subtitles = await services.youtubeApi.getCaptionsList(videoId);
    await services.cache.setYouTubeApiSubtitleList(videoId, subtitles);

    const duration = performance.now() - startTime;
    logger.info("API subtitles fetch completed", {
      videoId,
      subtitleCount: subtitles.length,
      duration: Math.round(duration),
    });
  },

  /**
   * Fetch and cache YouTube API metadata (blocking)
   */
  fetchApiMetadata: async ({
    videoId,
    services,
  }: TaskContext): Promise<void> => {
    const startTime = performance.now();
    logger.debug("Starting API metadata fetch", { videoId });

    const metadata = await services.youtubeApi.getVideoMetadata(videoId);
    await services.cache.setYouTubeApiMetadata(videoId, metadata);

    const duration = performance.now() - startTime;
    logger.info("API metadata fetch completed", {
      videoId,
      duration: Math.round(duration),
      title: metadata.title,
    });
  },

  /**
   * Start yt-dlp processing with distributed locking
   */
  processWithYtDlp: async ({
    videoId,
    services,
  }: TaskContext): Promise<void> => {
    const startTime = performance.now();
    logger.debug("Starting yt-dlp processing", { videoId });

    const lock = await services.cache.acquireLock(videoId);
    if (!lock) {
      logger.info("Video processing already in progress", { videoId });
      return;
    }

    logger.debug("Acquired processing lock", { videoId, lockId: lock.lock_id });

    try {
      await services.apify.startActorRun({
        videoUrl: `https://www.youtube.com/watch?v=${videoId}`,
        lockId: lock.lock_id,
      });

      const duration = performance.now() - startTime;
      logger.info("yt-dlp processing initiated", {
        videoId,
        lockId: lock.lock_id,
        duration: Math.round(duration),
      });
      // revalidation submitted and handled asynchronously by webhooks
    } catch (error) {
      const duration = performance.now() - startTime;
      logger.error("yt-dlp processing failed", {
        videoId,
        lockId: lock.lock_id,
        error: error instanceof Error ? error.message : String(error),
        duration: Math.round(duration),
      });

      await services.cache.releaseLock(
        lock.lock_id,
        videoId,
        "failed",
        error instanceof Error ? error.message : String(error),
      );
      throw error; // Re-throw to trigger retry mechanism
    }
  },
} as const;

/**
 * High-level revalidation service that combines task execution with predefined tasks
 */
export class RevalidationService {
  #executor: TaskExecutor;
  #context: TaskContext;

  constructor(
    videoId: string,
    services: TaskContext["services"],
    executionCtx: ExecutionContext,
    options?: TaskExecutionOptions,
  ) {
    this.#executor = new TaskExecutor(executionCtx, options);
    this.#context = { videoId, services };
  }

  /**
   * Revalidate YouTube API subtitle list in background
   */
  revalidateApiSubtitles(): void {
    logger.debug("Scheduling API subtitles revalidation", {
      videoId: this.#context.videoId,
    });
    this.#executor.enqueue(YouTubeTasks.fetchApiSubtitles, this.#context);
  }

  /**
   * Revalidate YouTube API metadata in background
   */
  revalidateApiMetadata(): void {
    logger.debug("Scheduling API metadata revalidation", {
      videoId: this.#context.videoId,
    });
    this.#executor.enqueue(YouTubeTasks.fetchApiMetadata, this.#context);
  }

  /**
   * Start yt-dlp processing in background
   */
  revalidateWithYtDlp(): void {
    logger.debug("Scheduling yt-dlp revalidation", {
      videoId: this.#context.videoId,
    });
    this.#executor.enqueue(YouTubeTasks.processWithYtDlp, this.#context);
  }

  /**
   * Revalidate all YouTube API data (subtitles + metadata) in parallel
   */
  revalidateAllApiData(): void {
    logger.debug("Scheduling all API data revalidation", {
      videoId: this.#context.videoId,
    });
    this.#executor.enqueueAll(
      [YouTubeTasks.fetchApiSubtitles, YouTubeTasks.fetchApiMetadata],
      this.#context,
    );
  }

  /**
   * Full revalidation: API data + yt-dlp processing
   */
  revalidateAll(): void {
    logger.debug("Scheduling full revalidation", {
      videoId: this.#context.videoId,
    });
    this.#executor.enqueueAll(
      [
        YouTubeTasks.fetchApiSubtitles,
        YouTubeTasks.fetchApiMetadata,
        YouTubeTasks.processWithYtDlp,
      ],
      this.#context,
    );
  }

  /**
   * Execute a custom task
   */
  executeCustomTask<T>(task: TaskFn<T>): void {
    logger.debug("Scheduling custom task", {
      videoId: this.#context.videoId,
      taskName: task.name || "anonymous",
    });
    this.#executor.enqueue(task, this.#context);
  }
}

/**
 * Factory function for creating revalidation services
 */
export function createRevalidationService(
  videoId: string,
  services: TaskContext["services"],
  executionCtx: ExecutionContext,
  options?: TaskExecutionOptions,
): RevalidationService {
  return new RevalidationService(videoId, services, executionCtx, options);
}

/**
 * Utility function for one-off task execution
 */
export function executeTask<T>(
  task: TaskFn<T>,
  context: TaskContext,
  executionCtx: ExecutionContext,
  options?: TaskExecutionOptions,
): void {
  const executor = new TaskExecutor(executionCtx, options);
  executor.enqueue(task, context);
}

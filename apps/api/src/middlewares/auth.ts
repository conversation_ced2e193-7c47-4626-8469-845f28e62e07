import { createClient, type User } from "@supabase/supabase-js";
import type { MiddlewareHandler } from "hono";
import { env } from "hono/adapter";
import { getLogger } from "@logtape/logtape";
import { redactSensitiveData } from "../lib/logging";
// import { setCookie } from "hono/cookie";

const logger = getLogger(["mx-api", "auth"]);

declare module "hono" {
  interface ContextVariableMap {
    user: User;
  }
}

export const authMiddleware = (): MiddlewareHandler => {
  return async (c, next) => {
    const startTime = performance.now();
    const requestId = crypto.randomUUID();

    logger.info("Authentication middleware processing request", {
      requestId,
      method: c.req.method,
      path: c.req.path,
      userAgent: c.req.header("User-Agent"),
    });

    const supabaseEnv =
      env<Pick<Cloudflare.Env, "SUPABASE_URL" | "SUPABASE_ANON_KEY">>(c);
    const supabaseUrl = supabaseEnv.SUPABASE_URL;
    const supabaseAnonKey = supabaseEnv.SUPABASE_ANON_KEY;

    logger.debug("Supabase configuration loaded", {
      requestId,
      hasUrl: !!supabaseUrl,
      hasAnonKey: !!supabaseAnonKey,
    });

    const authHeader = c.req.header("Authorization");

    if (!authHeader) {
      const duration = performance.now() - startTime;
      logger.warn("Authentication failed - no Authorization header", {
        requestId,
        method: c.req.method,
        path: c.req.path,
        duration: Math.round(duration),
      });
      return c.text("Unauthorized", 401);
    }

    logger.debug("Authorization header found, creating Supabase client", {
      requestId,
      authHeaderPresent: true,
    });

    const supabase = createClient(supabaseUrl, supabaseAnonKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
        detectSessionInUrl: false,
      },
      global: {
        headers: { Authorization: authHeader },
      },
    });

    const accessToken = authHeader.replace("Bearer ", "");
    const tokenPreview = accessToken.substring(0, 10) + "...";

    logger.debug("Validating access token with Supabase", {
      requestId,
      tokenPreview,
    });

    const { data, error } = await supabase.auth.getUser(accessToken);

    if (error) {
      const duration = performance.now() - startTime;
      logger.error("Authentication failed - Supabase user validation error", {
        requestId,
        error: redactSensitiveData({
          message: error.message,
          name: error.name,
          status: error.status,
        }),
        tokenPreview,
        duration: Math.round(duration),
      });
      return c.text("Unauthorized", 401);
    }

    const duration = performance.now() - startTime;
    logger.info("Authentication successful", {
      requestId,
      userId: data.user.id,
      email: redactSensitiveData({ email: data.user.email }).email,
      userMetadata: !!data.user.user_metadata,
      duration: Math.round(duration),
    });

    c.set("user", data.user);
    await next();

    // const cookieMethods: CookieMethodsServer = {
    //   getAll() {
    //     return parseCookieHeader(c.req.header("Cookie") ?? "");
    //   },
    //   setAll(cookiesToSet) {
    //     for (const { name, value, options } of cookiesToSet) {
    //       setCookie(c, name, value, {
    //         ...options,
    //         sameSite:
    //           options.sameSite === true
    //             ? "Strict"
    //             : options.sameSite === false
    //               ? undefined
    //               : options.sameSite,
    //         priority:
    //           options.priority === "high"
    //             ? "High"
    //             : options.priority === "medium"
    //               ? "Medium"
    //               : options.priority === "low"
    //                 ? "Low"
    //                 : undefined,
    //       });
    //     }
    //   },
    // };
  };
};
